import { readFileSync, writeFileSync } from "fs";
import path from "path";
import dotenv from "dotenv";

/** @param {object} changes */
export default (changes) => {
    if (!changes || typeof changes !== "object") return;

    const envPath = path.join(process.cwd(), ".env");
    const env = dotenv.parse(readFileSync(envPath, "utf-8"));

    for (const [k, v] of Object.entries(changes)) {
        env[k] = v;
        process.env[k] = v
    }

    const newEnvContent = Object.entries(env).map(([k, v]) => `${k}=${v}`).join("\n");
    writeFileSync(envPath, newEnvContent, "utf-8");
}
