import { Events } from "discord.js";
import { readdirSync } from "fs"
import path from "path"
import log from "./log";
import chalk from "chalk";

/** @param {import('../../structures/Client').default} client */
export default (client) => {
    const _path = path.join(process.cwd(), 'src', 'events');
    const files = readdirSync(path.join(_path)).filter(f => f.endsWith('.js'));

    for (const file of files) {
        /** @type {import('../../structures/Event').default<string>} */
        const event = require(path.join(_path, file)).default;

        if (!Object.values(Events).includes(event.name)) return log('e', `Geçersiz event ismi: ${chalk.redBright(event.name)}  ——  ${chalk.underline.redBright(`src/events/${file}`)}`);
        if (typeof event.execute !== 'function') return log('e', `Eventin execute fonksiyonu yok: ${chalk.underline.redBright(`src/events/${file}`)}`);

        if (event.once) {
            client.once(event.name, (...args) => event.execute(client, ...args));
        } else {
            client.on(event.name, (...args) => event.execute(client, ...args));
        }
    }
}