import readline from "readline";
import log from "../general/log";
import checkToken from "../general/checkToken";
import modifyEnv from "../general/modifyEnv";
import chalk from "chalk";

export default () => new Promise(resolve => {

    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    log('e', 'Botun tokeni geçersiz.');

    askToken();

    function askToken() {
        rl.question(chalk.blueBright.underline('> Yeni tokeni girin:') + ' ', async token => {
            if (!token) askToken();
            log('i', 'Token kontrol ediliyor.');
            await checkToken(token)
                .then(() => {
                    console.clear();
                    modifyEnv({ BOT_TOKEN: token });
                    rl.close();
                    log('o', 'Token başarıyla ayarlandı.');

                    setTimeout(() => {
                        console.clear();
                        resolve();
                    }, 5000);
                }).catch(e => {
                    log('e', e.message)
                    return askToken();
                })
        })
    }
})