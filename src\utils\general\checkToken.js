import { Client } from "discord.js";

export default (token) => new Promise((resolve, reject) => {
    const client = new Client({ intents: 0 });

    client.once("ready", () => {
        client.destroy();
        resolve(true);
    });

    client.once("error", e => {
        client.destroy();
        reject(e);
    });

    client.login(token).catch(e => {
        client.destroy();
        reject(e);
    });
});