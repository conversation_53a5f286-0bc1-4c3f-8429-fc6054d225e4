import { readdirSync } from "fs"
import path from "path"
import log from "./log";
import chalk from "chalk";

/** @param {import('../../structures/Client').default} client */
export default (client) => {
    const _path = path.join(process.cwd(), 'src', 'commands');
    const files = readdirSync(path.join(_path)).filter(f => f.endsWith('.js'));

    for (const file of files) {
        /** @type {import('../../structures/Command').default} */
        const command = require(path.join(_path, file)).default;

        if (!command.data || typeof command.data !== 'object') return log('e', `Komutun ${chalk.yellowBright('data')} özelliği yok: ${chalk.underline.redBright(`src/commands/${file}`)}`);
        if (!command.data.name || !command.data.description) return log('e', `Komutun data özelliği geçersiz: ${chalk.underline.redBright(`src/commands/${file}`)}`);
        if (!command.execute || typeof command.execute !== 'function') return log('e', `Komutun execute fonksiyonu yok: ${chalk.underline.redBright(`src/commands/${file}`)}`);

        client.commands.set(command.data.name, command);
    }
}