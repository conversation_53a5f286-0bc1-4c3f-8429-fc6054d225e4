import { JsonDatabase } from "dev.db";
import { Collection, Client as DiscordClient } from "discord.js";

export default class Client extends DiscordClient {

    /** @param {import("discord.js").ClientOptions?} options */
    constructor(options) {
        super(options);

        /** @type {import("discord.js").Collection<string, import("./structures/Command").default>} */
        this.commands = new Collection();

        /** @type {import("discord.js").Collection<string, number>} */
        this.cooldowns = new Collection();

        /** @type {JsonDatabase} */
        this.db = new JsonDatabase({ autoSave: true, dataFile: 'database.json' })
    }

    /** @param {string} k */
    getCooldown(k) {
        const v = this.cooldowns.get(k);
        if (v && v <= Date.now()) {
            this.cooldowns.delete(k);
            return null;
        }
        return v;
    }

    /**
     * @param {string} k 
     * @param {number} v 
     */
    setCooldown(k, v) {
        this.cooldowns.set(k, v);
    }
}