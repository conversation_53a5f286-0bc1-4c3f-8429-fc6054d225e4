import chalk from "chalk";
import Event from "../structures/Event";
import log from "../utils/general/log";
import registerCommands from "../utils/general/registerCommands";

export default new Event({
    name: 'ready',
    async execute(client) {

        log('o', `${chalk.cyanBright(client.user.tag)} olarak giriş yapıldı.`);

        client.user.setStatus(client.db.get('status') || 'online');

        await registerCommands(client);

    }
})