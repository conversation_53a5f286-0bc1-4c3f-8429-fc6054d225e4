console.clear();
require('dotenv').config();
import Client from "./structures/Client";
import NoToken from "./utils/errors/NoToken";
import TokenInvalid from "./utils/errors/TokenInvalid";
import loadCommands from "./utils/general/loadCommands";
import loadEvents from "./utils/general/loadEvents";

const client = new Client({ intents: [] });

loadEvents(client);
loadCommands(client);

if (!process.env.BOT_TOKEN) {
    await NoToken();
}
client.login(process.env.BOT_TOKEN).catch(async () => {
    await TokenInvalid();
    client.login(process.env.BOT_TOKEN);
})