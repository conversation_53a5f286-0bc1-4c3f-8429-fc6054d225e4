import { REST, Routes } from 'discord.js';
import log from './log';
import chalk from 'chalk';

/** @param {import('../../structures/Client').default} client */
export default async (client) => {

    const commands = client.commands.map(c => c.data);
    if (!commands.length) return;

    const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);

    try {
        await rest.put(Routes.applicationCommands(client.application.id), { body: commands });

        log('o', `${chalk.greenBright(commands.length)} adet komut (/) yüklendi.`)
    } catch (error) {
        log('e', `Eğik çizgi komutları (/) yüklenemedi: ${error.message}`)
        process.exit(1)
    }

}